﻿using System.Text.Json;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Lawsuits.Infrastructure.DataDivergence;

internal sealed class DataDivergenceConfiguration : IEntityTypeConfiguration<Domain.Lawsuits.DataDivergence>
{
    public void Configure(EntityTypeBuilder<Domain.Lawsuits.DataDivergence> builder)
    {
        builder.ToTable("data_divergence");

        builder.HasQueryFilter(dd => dd.DeletedAt == null);

        builder.HasKey(dd => dd.Id);
        builder.Property(dd => dd.Id).HasColumnType("uuid");

        builder.Property(dd => dd.LawsuitId)
            .HasColumnName("lawsuit_id")
            .IsRequired();
        
        builder.Property(dd => dd.OfficeId)
            .HasColumnName("office_id")
            .IsRequired();
        
        builder.Property(dd => dd.InstanceId)
            .HasColumnName("instance_id")
            .IsRequired();
        
        var serializationOptions = new JsonSerializerOptions();
        
        builder.Property(dd => dd.FieldsJson)
            .HasColumnName("fields_json")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, serializationOptions),
                v => v)
            .HasDefaultValueSql("'{}'::jsonb");
        
        builder.Property(dd => dd.CreatedAt)
            .HasDefaultValueSql("NOW()");
        
        builder.Property(dd => dd.UpdatedAt);
        
        builder.Property(dd => dd.AnalyzedAt);
        
        builder.Property(dd => dd.AnalyzedBy);
        
        builder.Property(dd => dd.WasAccepted);
    }
}
