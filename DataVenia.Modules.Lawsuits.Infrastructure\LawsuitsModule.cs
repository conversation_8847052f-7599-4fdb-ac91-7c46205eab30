﻿using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Application.Cases;
using DataVenia.Modules.Lawsuits.Application.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.CasesParties;
using DataVenia.Modules.Lawsuits.Domain.LawsuitParty;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Domain.Outbox;
using DataVenia.Modules.Lawsuits.Infrastructure.CaseParty;
using DataVenia.Modules.Lawsuits.Infrastructure.Cases;
using DataVenia.Modules.Lawsuits.Infrastructure.CasesData;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using DataVenia.Modules.Lawsuits.Infrastructure.LawsuitData;
using DataVenia.Modules.Lawsuits.Infrastructure.LawsuitParty;
using DataVenia.Modules.Lawsuits.Infrastructure.Lawsuits;
using DataVenia.Modules.Lawsuits.Infrastructure.LawsuitStep;
using DataVenia.Modules.Lawsuits.Infrastructure.Outbox;
using DataVenia.Modules.Lawsuits.Presentation;
using DataVenia.Modules.Lawsuits.Presentation.Consumers;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Infrastructure;

public static class LawsuitsModule
{
    public static IServiceCollection AddLawsuitsModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddInfrastructure(configuration);

        services.AddEndpoints(AssemblyReference.Assembly);

        return services;
    }
    
    public static IBusRegistrationConfigurator RegisterLawsuitsModuleConsumer(
        this IBusRegistrationConfigurator configurator)
    {
        configurator = configurator ?? throw new ArgumentNullException(nameof(configurator));
        
        configurator.AddConsumer<LawsuitUpdateEventConsumer>();

        return configurator;
    }

    private static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<LawsuitsDbContext>((sp, options) =>
            options
                .UseNpgsql(
                    configuration.GetConnectionString("Database"),
                    npgsqlOptions => npgsqlOptions
                    .MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Lawsuit))
                .EnableDetailedErrors()
                .UseLoggerFactory(sp.GetRequiredService<ILoggerFactory>())
                .EnableSensitiveDataLogging()
                .UseSnakeCaseNamingConvention());


        services.AddScoped<ILawsuitRepository, LawsuitRepository>();
        services.AddScoped<ILawsuitDataRepository, LawsuitDataRepository>();
        services.AddScoped<ILawsuitPartyRepository, LawsuitPartyRepository>();
        
        services.AddScoped<ICaseRepository, CaseRepository>();
        services.AddScoped<ICaseDataRepository, CaseDataRepository>();
        services.AddScoped<ICasePartyRepository, CasePartyRepository>();
        services.AddScoped<IOutboxRepository, OutboxRepository>();
        services.AddScoped<ILawsuitStepRepository, LawsuitStepRepository>();
        
        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<LawsuitsDbContext>());
    }
}
